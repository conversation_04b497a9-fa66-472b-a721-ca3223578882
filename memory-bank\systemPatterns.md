# System Patterns: Web Portfolio

**System Architecture:**

*   Component-based architecture using Next.js.
*   Utilizing Tailwind CSS for styling and responsive design.
*   Implementing animations with Framer Motion.

**Key Technical Decisions:**

*   Using Next.js for server-side rendering and routing.
*   Choosing Tailwind CSS for rapid UI development and consistency.
*   Leveraging Framer Motion for declarative and performant animations.

**Design Patterns:**

*   Reusable components for UI elements.
*   CSS utility classes for styling.
*   Motion variants for animation states.

**Component Relationships:**

*   `Navbar` component for navigation.
*   `Services` component for showcasing services.
*   `Page` component for the main page layout.
