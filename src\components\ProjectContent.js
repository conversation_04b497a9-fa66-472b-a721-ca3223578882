"use client";

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  // Fun background colors for each project to see the mask effect
  const backgroundColors = [
    'bg-blue-500',    // Project 1 - Blue
    'bg-green-500',   // Project 2 - Green
    'bg-purple-500',  // Project 3 - Purple
    'bg-orange-500'   // Project 4 - Orange
  ];

  const projectIndex = projects.findIndex(p => p.id === project.id);
  const bgColor = backgroundColors[projectIndex] || 'bg-primary';

  return (
    <div
      className="absolute inset-0 w-full h-full"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
    >
      {/* Visual showcase content */}
      <div className={`p-8 h-full flex items-center justify-center ${bgColor} rounded-3xl`}>
        <div className="text-center">
          {/* Project preview image area */}
          <div className="aspect-square bg-primary/20 rounded-2xl mb-6 flex items-center justify-center overflow-hidden mx-auto max-w-xs">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
            {/* Future: Replace with actual project image */}
            {/* <img src={project.image} alt={project.title} className="w-full h-full object-cover" /> */}
          </div>

          {/* Project showcase details */}
          <div className="space-y-2">
            <div className="text-sm text-secondary">Live Demo</div>
            <div className="text-sm text-secondary">Gallery • Screenshots</div>
          </div>

          {/* Project indicator */}
          <div className="mt-4 flex justify-center space-x-2">
            {projects.map((_, dotIndex) => (
              <div
                key={dotIndex}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  dotIndex === Math.min(activeProjectIndex, projects.length - 1) ? 'bg-primary' : 'bg-primary/30'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectContent;
