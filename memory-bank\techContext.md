# Tech Context: Web Portfolio

**Technologies Used:**

*   Next.js: Version 14 (or latest)
*   Tailwind CSS: Version 3 (or latest)
*   Framer Motion: Version 10 (or latest)
*   JavaScript (ES6+)

**Development Setup:**

*   VS Code as the primary code editor.
*   Node.js and npm for package management.
*   Git for version control.

**Technical Constraints:**

*   Browser compatibility for modern browsers.
*   Performance optimization for smooth animations.
*   Adherence to web accessibility standards.

**Dependencies:**

*   `react`
*   `react-dom`
*   `next`
*   `tailwindcss`
*   `autoprefixer`
*   `postcss`
*   `framer-motion`
