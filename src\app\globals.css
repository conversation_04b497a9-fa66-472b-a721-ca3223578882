@import "tailwindcss";

@theme {
  /* Your custom colors - now theme-aware */
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-accent: #f26430;     /* Your accent color stays the same */

  /* Your custom fonts */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

html {
  scroll-behavior: smooth;
}

html, body {
  height: 100%;
  height: 100dvh;
}

/* Default theme variables (dark mode) */
:root {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Light mode theme */
:root[data-theme="light"] {
  --color-primary: #ffffff;       /* Light background (was secondary) */
  --color-secondary: #0b0505;     /* Dark text (was primary) */
  --color-background: #ffffff;    /* Light background */
  --color-foreground: #0b0505;    /* Dark text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 255, 234, 214;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Dark mode theme (explicit) */
:root[data-theme="dark"] {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans);
}

.scroll-down-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Safari mobile viewport fix */
.h-screen {
  height: 100vh;
  height: 100dvh;
}

.min-h-screen {
  min-height: 100vh;
  min-height: 100dvh;
}

/* Cursor Effect Styles */

/* Base texture layer - sits beneath main background */
.cursor-texture-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.15;
  transition: opacity 0.3s ease;
}

/* Dark theme texture */
:root[data-theme="dark"] .cursor-texture-layer {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 234, 214, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(242, 100, 48, 0.05) 0%, transparent 50%),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 234, 214, 0.02) 2px,
      rgba(255, 234, 214, 0.02) 4px
    );
}

/* Light theme texture */
:root[data-theme="light"] .cursor-texture-layer {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(11, 5, 5, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(242, 100, 48, 0.04) 0%, transparent 50%),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(11, 5, 5, 0.015) 2px,
      rgba(11, 5, 5, 0.015) 4px
    );
}

/* Cursor mask overlay */
.cursor-mask-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  background: var(--color-primary);
  transition: opacity 0.3s ease;
}

/* Mask that reveals texture underneath */
.cursor-mask {
  width: 100%;
  height: 100%;
  mask: radial-gradient(
    circle at var(--cursor-x, 50%) var(--cursor-y, 50%),
    transparent 0px,
    transparent 80px,
    rgba(0, 0, 0, 0.8) 120px,
    black 160px
  );
  -webkit-mask: radial-gradient(
    circle at var(--cursor-x, 50%) var(--cursor-y, 50%),
    transparent 0px,
    transparent 80px,
    rgba(0, 0, 0, 0.8) 120px,
    black 160px
  );
}

/* Hide cursor effect on mobile devices */
@media (hover: none) and (pointer: coarse) {
  .cursor-texture-layer,
  .cursor-mask-overlay {
    display: none;
  }
}

/* Smooth cursor movement */
.cursor-mask-overlay {
  will-change: mask-position;
}

/* Performance optimization */
.cursor-texture-layer,
.cursor-mask-overlay {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Navbar hover effects */
.navbar-item {
  position: relative;
  transition: color 0.3s ease;
}

.navbar-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-item:hover::after {
  width: 100%;
}

.navbar-item:hover {
  color: var(--color-accent);
}

/* Button hover effect - simple fade version with border */
.request-project-btn {
  transition: all 0.1s ease;
}

.request-project-btn:hover {
  background-color: var(--color-accent) !important;
  color: var(--color-primary);
}
