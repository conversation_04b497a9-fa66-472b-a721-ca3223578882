"use client";

import { useRef, useState, useEffect } from 'react';
import AnimatedFixedTitle from './AnimatedFixedTitle';

const SectionTitleAnimation = ({ 
  title,
  currentSectionRef,
  previousSectionSelector, // e.g., '[data-section="services"]'
  zIndex = "z-5",
  className = "font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
}) => {
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [sectionScrollProgress, setSectionScrollProgress] = useState(0);
  const [isSectionComplete, setIsSectionComplete] = useState(false);
  const [normalScrollProgress, setNormalScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (!currentSectionRef.current) return;

      const currentRect = currentSectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Special case for Services (first section after Home) - no previous section with data attribute
      if (previousSectionSelector === '[data-section="home"]') {
        // Services is the first section after Home, so trigger when Services section comes into view
        const currentTop = currentRect.top;

        // Show title when Services section is approaching the viewport
        const triggerPoint = windowHeight * 0.5; // Show title when Services is 50% down the viewport
        const shouldShowTitle = currentTop <= triggerPoint;

        // Show animation when scrolling down and condition is met
        if (shouldShowTitle && !hasAnimatedIn) {
          setHasAnimatedIn(true);
          setTitleVisible(true);
        }

        // Hide animation when scrolling back up past the same trigger point
        if (!shouldShowTitle && hasAnimatedIn) {
          setHasAnimatedIn(false);
          setTitleVisible(false);
        }
      } else {
        // Normal case - find the previous section to determine when to show title
        const previousSection = document.querySelector(previousSectionSelector);
        if (!previousSection) return;

        const previousRect = previousSection.getBoundingClientRect();

        // Calculate when previous section is mostly scrolled past
        const previousBottom = previousRect.bottom;
        const previousTop = previousRect.top;

        // Show title when previous section is almost gone
        const triggerPoint = windowHeight * 0.3;
        const previousAlmostGone = previousBottom <= triggerPoint;
        const previousStartedScrolling = previousTop <= windowHeight * 0.8;

        // Title appears when previous section is almost gone
        const shouldShowTitle = previousAlmostGone && previousStartedScrolling;

        // Show animation when scrolling down and condition is met
        if (shouldShowTitle && !hasAnimatedIn) {
          setHasAnimatedIn(true);
          setTitleVisible(true);
        }

        // Hide animation when scrolling back up past the same trigger point
        if (!shouldShowTitle && hasAnimatedIn) {
          setHasAnimatedIn(false);
          setTitleVisible(false);
        }
      }

      // Calculate scroll-driven effects when title is visible
      if (titleVisible) {
        const currentTop = currentRect.top;
        const currentHeight = currentRect.height;

        // Start animations when current section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (currentTop <= triggerOffset) {
          // Calculate scroll progress through section with three phases:
          // Phase 1: Section animations (0-75% of section height) - SLOW & SMOOTH
          // Phase 2: Settling period (75-80% of section height) - PAUSE
          // Phase 3: Normal scroll transition (80-100% = exactly 100vh) - NATURAL SPEED
          const scrolledIntoSection = Math.abs(currentTop - triggerOffset);

          const sectionAnimationDistance = currentHeight * 0.75; // 75% for smooth animations
          const settlingDistance = currentHeight * 0.05;         // 5% for settling
          const normalScrollDistance = currentHeight * 0.2;      // 20% for normal scroll
          const totalAnimationDistance = sectionAnimationDistance + settlingDistance;

          const rawScrollProgress = Math.min(1, scrolledIntoSection / sectionAnimationDistance);
          const settlingProgress = Math.min(1, Math.max(0, (scrolledIntoSection - sectionAnimationDistance) / settlingDistance));

          // Section animation is complete after settling period finishes
          const sectionAnimationComplete = settlingProgress >= 1.0;

          if (!sectionAnimationComplete) {
            // Phase 1: Section animations (0% - 75% of section height)
            setIsSectionComplete(false);
            setNormalScrollProgress(0);

            // Add a delay buffer - title stays crisp for the first 10% of scroll progress
            const delayBuffer = 0.10;
            const adjustedScrollProgress = Math.max(0, (rawScrollProgress - delayBuffer) / (1 - delayBuffer));

            // Apply scroll-driven effects to title with smoother transitions (only after delay)
            const opacity = adjustedScrollProgress > 0 ? Math.max(0, 1 - (adjustedScrollProgress * 3)) : 1;
            const blur = adjustedScrollProgress * 10;
            const scale = adjustedScrollProgress > 0 ? Math.max(0.8, 1 - (adjustedScrollProgress * 0.5)) : 1;

            setScrollEffects({ opacity, blur, scale });
            setSectionScrollProgress(rawScrollProgress);
          } else {
            // Phase 3: Section complete (after settling), calculate normal scroll progress
            setIsSectionComplete(true);
            setSectionScrollProgress(1); // Keep section at final state

            // Calculate normal scroll progress for the remaining 20% of section height
            const normalScrollStart = totalAnimationDistance; // Start after settling period
            const normalScrollAmount = Math.max(0, scrolledIntoSection - normalScrollStart);
            const normalProgress = Math.min(1, normalScrollAmount / normalScrollDistance);

            setNormalScrollProgress(normalProgress);

            // Keep title effects at final state during normal scroll
            setScrollEffects({ opacity: 0, blur: 15, scale: 0.8 });
          }
        } else {
          // Reset effects when section hasn't reached the trigger point yet
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setSectionScrollProgress(0);
          setIsSectionComplete(false);
          setNormalScrollProgress(0);
        }
      } else {
        // Title not visible yet - reset everything
        setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        setSectionScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible, currentSectionRef, previousSectionSelector]);

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects */}
      <div
        className={`fixed inset-0 flex items-center justify-center pointer-events-none ${zIndex}`}
        style={{
          transform: isSectionComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        <AnimatedFixedTitle
          title={title}
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className={className}
          containerClassName=""
        />
      </div>
    </>
  );
};

export default SectionTitleAnimation;
