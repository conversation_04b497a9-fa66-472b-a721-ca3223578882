"use client";

import { motion } from 'framer-motion';

const AnimatedFixedTitle = ({ 
  title, 
  titleVisible,
  scrollEffects = { opacity: 1, blur: 0, scale: 1 },
  className = "font-heading font-extrabold text-secondary text-4xl lg:text-6xl",
  containerClassName = "fixed inset-0 flex items-center justify-center pointer-events-none z-5"
}) => {
  const containerVariants = {
    hidden: {
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1, // Reverse order for exit animation
        delayChildren: 0
      }
    },
    visible: {
      transition: {
        staggerChildren: 0.02,
      }
    }
  };
  
  const letterVariants = {
    hidden: {
      opacity: 0,
      y: 40,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const letters = title.split("");

  return (
    <motion.div
      className={containerClassName}
      initial="hidden"
      animate={titleVisible ? "visible" : "hidden"}
      variants={containerVariants}
      style={{
        filter: `blur(${titleVisible ? scrollEffects.blur : 0}px)`,
        transform: `scale(${titleVisible ? scrollEffects.scale : 1})`,
        opacity: titleVisible ? scrollEffects.opacity : undefined // Let Framer Motion handle opacity during transitions
      }}
    >
      <motion.h2 
        className={className}
        variants={containerVariants}
      >
        {letters.map((letter, index) => (
          <motion.span
            key={index}
            variants={letterVariants}
            className="inline-block"
          >
            {letter}
          </motion.span>
        ))}
      </motion.h2>
    </motion.div>
  );
};

export default AnimatedFixedTitle;
