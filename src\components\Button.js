"use client";

import { motion } from "framer-motion";

const Button = ({ 
  children, 
  variant = "filled", // "filled" or "outline"
  size = "medium", // "small", "medium", "large"
  onClick,
  href,
  className = "",
  disabled = false,
  ...props 
}) => {
  // Base styles
  const baseStyles = "request-project-btn font-semibold rounded-full focus:outline-none focus:ring-2 focus:ring-accent/50 disabled:opacity-50 disabled:cursor-not-allowed";

  // Size variants
  const sizeStyles = {
    small: "px-4 py-2 text-sm",
    medium: "px-6 py-2 text-base",
    large: "px-8 py-3 text-lg"
  };

  // Variant styles with hover animations
  const variantStyles = {
    filled: "bg-secondary text-primary",
    outline: "border border-secondary text-secondary bg-transparent hover:bg-secondary/10 hover:border-secondary/80"
  };
  
  // Combine all styles
  const buttonStyles = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${className}`;
  
  // Motion variants for tap animation only
  const buttonVariants = {
    initial: { scale: 1 },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 }
    }
  };

  // If href is provided, render as a link
  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonStyles}
        variants={buttonVariants}
        initial="initial"
        whileTap="tap"
        {...props}
      >
        {children}
      </motion.a>
    );
  }

  // Otherwise render as button
  return (
    <motion.button
      onClick={onClick}
      className={buttonStyles}
      disabled={disabled}
      variants={buttonVariants}
      initial="initial"
      whileTap="tap"
      {...props}
    >
      {children}
    </motion.button>
  );
};

export default Button;
