"use client";

import { motion } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';

const AnimatedTitle = ({ 
  title, 
  className = "font-heading font-extrabold text-secondary text-4xl lg:text-5xl",
  triggerPoint = 0.5, // When element should trigger (0.5 = 50% down from top of viewport)
  containerClassName = "text-center mb-16" // Container styling
}) => {
  const ref = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!ref.current) return;
      
      const rect = ref.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Element's top position relative to viewport
      const elementTop = rect.top;
      
      // Trigger point: configurable position in viewport
      const trigger = windowHeight * triggerPoint;
      
      // Show animation when scrolling down and element comes into view
      if (elementTop <= trigger && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setShouldShow(true);
      }
      
      // Hide animation when scrolling back up past the same trigger point
      if (elementTop > trigger && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setShouldShow(false);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, triggerPoint]);
  
  const containerVariants = {
    hidden: {
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1, // Reverse order for exit animation
        delayChildren: 0
      }
    },
    visible: {
      transition: {
        staggerChildren: 0.02,
      }
    }
  };
  
  const letterVariants = {
    hidden: {
      opacity: 0,
      y: 40,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const letters = title.split("");

  return (
    <div ref={ref} className={containerClassName}>
      <motion.h2 
        className={className}
        variants={containerVariants}
        initial="hidden"
        animate={shouldShow ? "visible" : "hidden"}
      >
        {letters.map((letter, index) => (
          <motion.span
            key={index}
            variants={letterVariants}
            className="inline-block"
          >
            {letter}
          </motion.span>
        ))}
      </motion.h2>
    </div>
  );
};

export default AnimatedTitle;
