/* Cursor Effect Styles */

/* Base texture layer - sits beneath main background */
.cursor-texture-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.15;
  transition: opacity 0.3s ease;
}

/* Dark theme texture */
:root[data-theme="dark"] .cursor-texture-layer {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 234, 214, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(242, 100, 48, 0.05) 0%, transparent 50%),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 234, 214, 0.02) 2px,
      rgba(255, 234, 214, 0.02) 4px
    );
}

/* Light theme texture */
:root[data-theme="light"] .cursor-texture-layer {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(11, 5, 5, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(242, 100, 48, 0.04) 0%, transparent 50%),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(11, 5, 5, 0.015) 2px,
      rgba(11, 5, 5, 0.015) 4px
    );
}

/* Cursor mask overlay */
.cursor-mask-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  background: var(--color-primary);
  transition: opacity 0.3s ease;
}

/* Mask that reveals texture underneath */
.cursor-mask {
  width: 100%;
  height: 100%;
  mask: radial-gradient(
    circle at var(--cursor-x, 50%) var(--cursor-y, 50%),
    transparent 0px,
    transparent 80px,
    rgba(0, 0, 0, 0.8) 120px,
    black 160px
  );
  -webkit-mask: radial-gradient(
    circle at var(--cursor-x, 50%) var(--cursor-y, 50%),
    transparent 0px,
    transparent 80px,
    rgba(0, 0, 0, 0.8) 120px,
    black 160px
  );
}

/* Hide cursor effect on mobile devices */
@media (hover: none) and (pointer: coarse) {
  .cursor-texture-layer,
  .cursor-mask-overlay {
    display: none;
  }
}

/* Smooth cursor movement */
.cursor-mask-overlay {
  will-change: mask-position;
}

/* Performance optimization */
.cursor-texture-layer,
.cursor-mask-overlay {
  transform: translateZ(0);
  backface-visibility: hidden;
}
