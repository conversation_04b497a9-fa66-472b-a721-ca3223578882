# Project Brief: Web Portfolio

**Project Goal:** Create a personal web portfolio to showcase skills and projects.

**Key Features:**

*   Visually appealing design with CSS animations.
*   Responsive layout for different screen sizes.
*   Clear and concise presentation of projects and skills.

**Technologies:**

*   Next.js
*   Tailwind CSS
*   Framer Motion

**Constraints:**

*   Maintainability and scalability of the codebase.
*   Performance optimization for smooth animations.
