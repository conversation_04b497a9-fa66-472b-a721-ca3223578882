"use client";

import React, { useEffect, useRef, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

const CursorEffect = () => {
  const maskRef = useRef(null);
  const textureRef = useRef(null);
  const { mounted } = useTheme();

  useEffect(() => {
    // Don't run on mobile devices or if not mounted
    if (!mounted || window.matchMedia('(hover: none) and (pointer: coarse)').matches) {
      return;
    }

    let animationFrameId;
    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;
    let currentX = mouseX;
    let currentY = mouseY;

    const handleMouseMove = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    const updateCursorPosition = () => {
      // Smooth interpolation for cursor movement
      const ease = 0.15;
      currentX += (mouseX - currentX) * ease;
      currentY += (mouseY - currentY) * ease;

      if (maskRef.current) {
        // Convert to pixels for CSS custom properties
        maskRef.current.style.setProperty('--cursor-x', `${currentX}px`);
        maskRef.current.style.setProperty('--cursor-y', `${currentY}px`);
      }

      animationFrameId = requestAnimationFrame(updateCursorPosition);
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);

    // Start animation loop
    updateCursorPosition();

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [mounted]);

  // Don't render anything if not mounted (SSR safety)
  if (!mounted) {
    return null;
  }

  console.log('CursorEffect rendering, mounted:', mounted);

  return (
    <>
      {/* Simple test div to see if component renders at all */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          left: '10px',
          width: '200px',
          height: '50px',
          background: 'red',
          color: 'white',
          zIndex: 9999,
          padding: '10px'
        }}
      >
        Cursor Effect Active
      </div>

      {/* Texture layer that sits beneath everything - MADE MORE VISIBLE FOR TESTING */}
      <div
        ref={textureRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'linear-gradient(45deg, #ff0000 50%, #00ff00 50%)',
          opacity: 0.5,
          zIndex: -1,
          pointerEvents: 'none'
        }}
      />

      {/* Mask overlay that reveals the texture */}
      <div
        ref={maskRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'var(--color-primary)',
          zIndex: -1,
          pointerEvents: 'none',
          mask: `radial-gradient(circle 100px at var(--cursor-x, 50vw) var(--cursor-y, 50vh), transparent 50px, black 100px)`,
          WebkitMask: `radial-gradient(circle 100px at var(--cursor-x, 50vw) var(--cursor-y, 50vh), transparent 50px, black 100px)`
        }}
      >
      </div>
    </>
  );
};

export default CursorEffect;
