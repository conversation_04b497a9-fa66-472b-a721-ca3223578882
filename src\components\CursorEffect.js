"use client";

import React, { useEffect, useRef, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

const CursorEffect = () => {
  const maskRef = useRef(null);
  const textureRef = useRef(null);
  const { mounted, theme } = useTheme();

  useEffect(() => {
    // Don't run on mobile devices or if not mounted
    if (!mounted || window.matchMedia('(hover: none) and (pointer: coarse)').matches) {
      return;
    }

    let animationFrameId;
    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;
    let currentX = mouseX;
    let currentY = mouseY;

    const handleMouseMove = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    const updateCursorPosition = () => {
      // Smooth interpolation for cursor movement
      const ease = 0.15;
      currentX += (mouseX - currentX) * ease;
      currentY += (mouseY - currentY) * ease;

      if (maskRef.current) {
        // Convert to pixels for CSS custom properties
        maskRef.current.style.setProperty('--cursor-x', `${currentX}px`);
        maskRef.current.style.setProperty('--cursor-y', `${currentY}px`);
      }

      animationFrameId = requestAnimationFrame(updateCursorPosition);
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);

    // Start animation loop
    updateCursorPosition();

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [mounted]);

  // Don't render anything if not mounted (SSR safety)
  if (!mounted) {
    return null;
  }

  // Define texture based on theme
  const getTextureBackground = () => {
    if (theme === 'light') {
      // Dark texture on light background
      return 'repeating-linear-gradient(45deg, rgba(11, 5, 5, 0.04) 0px, rgba(11, 5, 5, 0.04) 2px, transparent 2px, transparent 4px)';
    } else {
      // Light texture on dark background
      return 'repeating-linear-gradient(45deg, rgba(255, 234, 214, 0.03) 0px, rgba(255, 234, 214, 0.03) 2px, transparent 2px, transparent 4px)';
    }
  };

  return (
    <>
      {/* Texture layer that sits beneath everything - THEME AWARE */}
      <div
        ref={textureRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: getTextureBackground(),
          opacity: 0.8,
          zIndex: -1,
          pointerEvents: 'none'
        }}
      />

      {/* Mask overlay that reveals the texture */}
      <div
        ref={maskRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'var(--color-primary)',
          zIndex: -1,
          pointerEvents: 'none',
          mask: `radial-gradient(circle at var(--cursor-x, 50vw) var(--cursor-y, 50vh), transparent 0px, transparent 80px, black 120px)`,
          WebkitMask: `radial-gradient(circle at var(--cursor-x, 50vw) var(--cursor-y, 50vh), transparent 0px, transparent 80px, black 120px)`
        }}
      >
      </div>
    </>
  );
};

export default CursorEffect;
