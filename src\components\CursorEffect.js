"use client";

import React, { useEffect, useRef, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

const CursorEffect = () => {
  const maskRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const { mounted } = useTheme();

  useEffect(() => {
    // Don't run on mobile devices or if not mounted
    if (!mounted || window.matchMedia('(hover: none) and (pointer: coarse)').matches) {
      return;
    }

    let animationFrameId;
    let mouseX = 0;
    let mouseY = 0;
    let currentX = 0;
    let currentY = 0;

    const handleMouseMove = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      
      // Show the effect when mouse moves
      if (!isVisible) {
        setIsVisible(true);
      }
    };

    const handleMouseLeave = () => {
      // Hide the effect when mouse leaves the window
      setIsVisible(false);
    };

    const updateCursorPosition = () => {
      // Smooth interpolation for cursor movement
      const ease = 0.15;
      currentX += (mouseX - currentX) * ease;
      currentY += (mouseY - currentY) * ease;

      if (maskRef.current) {
        // Convert to percentage for CSS custom properties
        const xPercent = (currentX / window.innerWidth) * 100;
        const yPercent = (currentY / window.innerHeight) * 100;
        
        maskRef.current.style.setProperty('--cursor-x', `${xPercent}%`);
        maskRef.current.style.setProperty('--cursor-y', `${yPercent}%`);
      }

      animationFrameId = requestAnimationFrame(updateCursorPosition);
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseleave', handleMouseLeave);
    
    // Start animation loop
    updateCursorPosition();

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [mounted, isVisible]);

  // Don't render anything if not mounted (SSR safety)
  if (!mounted) {
    return null;
  }

  return (
    <>
      {/* Texture layer that sits beneath everything - MADE MORE VISIBLE FOR TESTING */}
      <div
        className="cursor-texture-layer"
        style={{
          opacity: isVisible ? 0.8 : 0,
          background: 'linear-gradient(45deg, #ff0000 25%, transparent 25%), linear-gradient(-45deg, #00ff00 25%, transparent 25%)',
          backgroundSize: '20px 20px'
        }}
      />

      {/* Mask overlay that reveals the texture */}
      <div
        ref={maskRef}
        className="cursor-mask-overlay"
        style={{
          opacity: isVisible ? 1 : 0
        }}
      >
        <div className="cursor-mask" />
      </div>
    </>
  );
};

export default CursorEffect;
